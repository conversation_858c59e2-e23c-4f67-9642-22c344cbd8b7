import { Beef, Utensils, Salad, Sandwich, CupSoda } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { Loader2, Star, Clock } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "./ui/sheet";

type MenuItem = {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  rating: number;
  prepTime: string;
  isPopular?: boolean;
  isNew?: boolean;
};

type CategoryDetails = {
  description: string;
  items: MenuItem[];
  image: string;
};

type CategoryItemProps = {
  image: string;
  title: string;
  description: string;
  onClick: () => void;
  index?: number;
  isVisible?: boolean;
};

const CategoryItem = ({
  image,
  title,
  description,
  onClick,
  index,
  isVisible,
}: CategoryItemProps) => {
  return (
    <div className={`w-full aspect-[1/1.2] h-[350px] transition-all duration-500 ${isVisible ? 'animate-bounce-in opacity-100' : 'opacity-0 translate-y-10'}`} style={{ animationDelay: `${index ? index * 100 : 0}ms` }}>
      <div className="bg-white p-4 rounded-xl cursor-pointer w-full h-full shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-100">
        <div className="flex flex-col items-center text-center h-full" onClick={onClick}>
          <div className="flex items-center justify-center h-[180px]">
            <div className="w-40 h-40">
              <img
                src={image}
                alt={title}
                className="object-contain w-full h-full"
              />
            </div>
          </div>
          <div className="h-[110px] flex flex-col justify-center">
            <div className="w-full">
              <h3 className="text-xl font-bold mb-1 text-gray-900">{title}</h3>
            </div>
            <div className="w-full">
              <p className="text-gray-600 text-sm">{description}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Detailed information for each category with demo menu items
const categoryDetails: Record<string, CategoryDetails> = {
  "Beef Burgers": {
    description: "Our beef burgers are made with 100% premium Angus beef, sourced from local farms. Each patty is hand-formed and grilled to perfection, locking in the juices and flavor.",
    items: [
      {
        id: "beef-1",
        name: "Classic Firefly Burger",
        description: "Our signature beef patty with lettuce, tomato, onion, and our special firefly sauce",
        price: 14.99,
        image: "https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "12-15 min",
        isPopular: true
      },
      {
        id: "beef-2",
        name: "Double Trouble",
        description: "Two juicy beef patties, double cheese, bacon, and all the fixings",
        price: 18.99,
        image: "https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=300&fit=crop",
        rating: 4.9,
        prepTime: "15-18 min",
        isPopular: true
      },
      {
        id: "beef-3",
        name: "Smoky BBQ Deluxe",
        description: "Beef patty with smoky BBQ sauce, crispy onions, and cheddar cheese",
        price: 16.99,
        image: "https://images.unsplash.com/photo-1613160775054-d4a634592b7f?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.7,
        prepTime: "12-15 min"
      },
      {
        id: "beef-4",
        name: "Mushroom Swiss Melt",
        description: "Beef patty topped with sautéed mushrooms and melted Swiss cheese",
        price: 15.99,
        image: "https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5?w=400&h=300&fit=crop",
        rating: 4.6,
        prepTime: "14-16 min"
      },
      {
        id: "beef-5",
        name: "Spicy Jalapeño Kick",
        description: "Beef patty with jalapeños, pepper jack cheese, and spicy mayo",
        price: 15.49,
        image: "https://images.unsplash.com/photo-1572802419224-296b0aeee0d9?w=400&h=300&fit=crop",
        rating: 4.5,
        prepTime: "12-15 min",
        isNew: true
      }
    ],
    image: "/categories/beef-burger.png"
  },
  "Chicken Burgers": {
    description: "Our chicken burgers feature tender, juicy chicken breast that's marinated in our secret blend of herbs and spices before being grilled or fried to crispy perfection.",
    items: [
      {
        id: "chicken-1",
        name: "Classic Chicken Burger",
        description: "Grilled chicken breast with lettuce, tomato, and mayo on a brioche bun",
        price: 13.99,
        image: "https://images.unsplash.com/photo-1594212699903-ec8a3eca50f5?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "10-12 min",
        isPopular: true
      },
      {
        id: "chicken-2",
        name: "Spicy Buffalo Chicken",
        description: "Crispy chicken tossed in buffalo sauce with blue cheese and celery",
        price: 15.99,
        image: "https://images.unsplash.com/photo-1572802419224-296b0aeee0d9?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "12-14 min"
      },
      {
        id: "chicken-3",
        name: "Honey Mustard Crispy",
        description: "Crispy chicken with honey mustard sauce, lettuce, and pickles",
        price: 14.49,
        image: "https://images.unsplash.com/photo-1655895176036-bf1a11326e5c?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.6,
        prepTime: "12-14 min"
      },
      {
        id: "chicken-4",
        name: "Avocado Ranch Chicken",
        description: "Grilled chicken with fresh avocado, ranch dressing, and bacon",
        price: 16.49,
        image: "https://images.unsplash.com/photo-1551782450-17144efb9c50?w=400&h=300&fit=crop",
        rating: 4.9,
        prepTime: "10-12 min",
        isNew: true
      },
      {
        id: "chicken-5",
        name: "BBQ Bacon Chicken",
        description: "Grilled chicken with BBQ sauce, crispy bacon, and onion rings",
        price: 17.99,
        image: "https://images.unsplash.com/photo-1603064752734-4c48eff53d05?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "12-15 min"
      }
    ],
    image: "/categories/chicken-burger.png"
  },
  "Veggie Burgers": {
    description: "Our plant-based options are crafted to satisfy even the most dedicated meat lovers. Made with fresh vegetables, legumes, and our special seasonings.",
    items: [
      {
        id: "veggie-1",
        name: "Garden Veggie Patty",
        description: "House-made veggie patty with fresh vegetables and herbs",
        price: 12.99,
        image: "https://images.unsplash.com/photo-1629680871149-434f75071693?q=80&w=735&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.5,
        prepTime: "10-12 min",
        isPopular: true
      },
      {
        id: "veggie-2",
        name: "Beyond Firefly Burger",
        description: "Plant-based Beyond patty with all the classic burger fixings",
        price: 16.99,
        image: "https://images.unsplash.com/photo-1520072959219-c595dc870360?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "12-14 min",
        isNew: true
      },
      {
        id: "veggie-3",
        name: "Portobello Mushroom Deluxe",
        description: "Grilled portobello mushroom with roasted peppers and goat cheese",
        price: 14.99,
        image: "https://images.unsplash.com/photo-1512152272829-e3139592d56f?w=400&h=300&fit=crop",
        rating: 4.6,
        prepTime: "10-12 min"
      },
      {
        id: "veggie-4",
        name: "Spicy Black Bean Burger",
        description: "Black bean patty with jalapeños, avocado, and chipotle mayo",
        price: 13.99,
        image: "https://images.unsplash.com/photo-1571091655789-405eb7a3a3a8?w=400&h=300&fit=crop",
        rating: 4.4,
        prepTime: "10-12 min"
      },
      {
        id: "veggie-5",
        name: "Mediterranean Falafel Burger",
        description: "Crispy falafel patty with hummus, cucumber, and tahini sauce",
        price: 15.49,
        image: "https://images.unsplash.com/photo-1529006557810-274b9b2fc783?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "12-14 min"
      }
    ],
    image: "/categories/veggie-burgers.png"
  },
  "Loaded Fries": {
    description: "Elevate your meal with our signature loaded fries. Crispy golden fries topped with premium ingredients for a flavor explosion in every bite.",
    items: [
      {
        id: "fries-1",
        name: "Firefly Loaded Fries",
        description: "Our signature fries loaded with cheese, bacon, and green onions",
        price: 9.99,
        image: "https://images.unsplash.com/photo-1573080496219-bb080dd4f877?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "8-10 min",
        isPopular: true
      },
      {
        id: "fries-2",
        name: "Cheese & Bacon Fries",
        description: "Golden fries topped with melted cheese and crispy bacon bits",
        price: 8.99,
        image: "https://images.unsplash.com/photo-1541592106381-b31e9677c0e5?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "6-8 min"
      },
      {
        id: "fries-3",
        name: "Truffle Parmesan Fries",
        description: "Gourmet fries with truffle oil, parmesan cheese, and herbs",
        price: 12.99,
        image: "https://images.unsplash.com/photo-1576107232684-1279f390859f?w=400&h=300&fit=crop",
        rating: 4.9,
        prepTime: "8-10 min",
        isNew: true
      },
      {
        id: "fries-4",
        name: "Chili Cheese Fries",
        description: "Fries topped with hearty chili and melted cheddar cheese",
        price: 10.99,
        image: "https://images.unsplash.com/photo-1630384060421-cb20d0e0649d?w=400&h=300&fit=crop",
        rating: 4.6,
        prepTime: "10-12 min"
      },
      {
        id: "fries-5",
        name: "Buffalo Ranch Fries",
        description: "Spicy buffalo sauce and cool ranch dressing over crispy fries",
        price: 9.49,
        image: "https://images.unsplash.com/photo-1541592391523-5ae8c2c88d10?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.5,
        prepTime: "6-8 min"
      }
    ],
    image: "/categories/loaded-fries.png"
  },
  "Drinks & Shakes": {
    description: "Quench your thirst with our selection of refreshing beverages or indulge in one of our hand-spun milkshakes made with premium ice cream.",
    items: [
      {
        id: "drinks-1",
        name: "Classic Vanilla Milkshake",
        description: "Rich and creamy vanilla milkshake topped with whipped cream",
        price: 5.99,
        image: "https://images.unsplash.com/photo-1572490122747-3968b75cc699?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "3-5 min",
        isPopular: true
      },
      {
        id: "drinks-2",
        name: "Chocolate Peanut Butter Shake",
        description: "Decadent chocolate and peanut butter milkshake with crushed peanuts",
        price: 6.99,
        image: "https://images.unsplash.com/photo-1541544181051-e46607bc22a4?w=400&h=300&fit=crop",
        rating: 4.9,
        prepTime: "3-5 min",
        isNew: true
      },
      {
        id: "drinks-3",
        name: "Fresh Strawberry Lemonade",
        description: "House-made lemonade with fresh strawberry puree",
        price: 4.49,
        image: "https://images.unsplash.com/photo-1513558161293-cdaf765ed2fd?w=400&h=300&fit=crop",
        rating: 4.6,
        prepTime: "2-3 min"
      },
      {
        id: "drinks-4",
        name: "Craft Root Beer Float",
        description: "Premium root beer with a scoop of vanilla ice cream",
        price: 5.49,
        image: "https://images.unsplash.com/photo-1541544181051-e46607bc22a4?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "2-3 min"
      },
      {
        id: "drinks-5",
        name: "Iced Coffee Frappe",
        description: "Cold brew coffee blended with ice and topped with whipped cream",
        price: 4.99,
        image: "https://images.unsplash.com/photo-1461023058943-07fcbe16d735?w=400&h=300&fit=crop",
        rating: 4.5,
        prepTime: "3-4 min"
      }
    ],
    image: "/categories/drinks.png"
  },
  "Sides": {
    description: "Complete your meal with our selection of sides. From crispy onion rings to creamy coleslaw, we have the perfect accompaniment to your burger.",
    items: [
      {
        id: "sides-1",
        name: "Crispy Onion Rings",
        description: "Golden beer-battered onion rings served with ranch dipping sauce",
        price: 6.99,
        image: "https://images.unsplash.com/photo-1639024471283-03518883512d?w=400&h=300&fit=crop",
        rating: 4.7,
        prepTime: "6-8 min",
        isPopular: true
      },
      {
        id: "sides-2",
        name: "Sweet Potato Fries",
        description: "Crispy sweet potato fries with a hint of cinnamon and sea salt",
        price: 5.99,
        image: "https://images.unsplash.com/photo-1529589510304-b7e994a92f60?q=80&w=687&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.6,
        prepTime: "8-10 min"
      },
      {
        id: "sides-3",
        name: "Mac & Cheese Bites",
        description: "Crispy fried mac and cheese balls with creamy cheese center",
        price: 7.99,
        image: "https://images.unsplash.com/photo-1574894709920-11b28e7367e3?w=400&h=300&fit=crop",
        rating: 4.8,
        prepTime: "6-8 min",
        isNew: true
      },
      {
        id: "sides-4",
        name: "House Coleslaw",
        description: "Fresh cabbage slaw with our signature creamy dressing",
        price: 3.99,
        image: "https://images.unsplash.com/photo-1537784969314-05a37106f68e?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
        rating: 4.4,
        prepTime: "1-2 min"
      },
      {
        id: "sides-5",
        name: "Mozzarella Sticks",
        description: "Golden fried mozzarella sticks served with marinara sauce",
        price: 6.49,
        image: "https://images.unsplash.com/photo-1548340748-6d2b7d7da280?w=400&h=300&fit=crop",
        rating: 4.5,
        prepTime: "6-8 min"
      }
    ],
    image: "/categories/sides.png"
  }
};

const BurgerCategories = () => {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [visibleCards, setVisibleCards] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);
  
  useEffect(() => {
    // Simulate loading time (remove in production and replace with actual data fetching)
    const loadingTimer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    const currentRef = sectionRef.current;
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !isLoading) {
          // Start showing cards with staggered delay
          const timer = setTimeout(() => {
            setVisibleCards(Array(6).fill(true)); // Fixed number of categories
          }, 100);
          return () => clearTimeout(timer);
        }
      },
      { threshold: 0.2 } // Trigger when 20% of the section is visible
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
      clearTimeout(loadingTimer);
    };
  }, [isLoading]);
  
  const categories = [
    {
      image: "/categories/beef-burger.png",
      title: "Beef Burgers",
      description: "Premium beef patties with our signature seasonings"
    },
    {
      image: "/categories/chicken-burger.png",
      title: "Chicken Burgers",
      description: "Juicy chicken with crispy coating and special sauces"
    },
    {
      image: "/categories/veggie-burgers.png",
      title: "Veggie Burgers",
      description: "Plant-based options that don't compromise on flavor"
    },
    {
      image: "/categories/loaded-fries.png",
      title: "Loaded Fries",
      description: "Crispy fries loaded with premium toppings"
    },
    {
      image: "/categories/drinks.png",
      title: "Drinks & Shakes",
      description: "Refreshing beverages and indulgent shakes"
    },
    {
      image: "/categories/sides.png",
      title: "Sides",
      description: "Perfect companions to complete your meal"
    },
  ];

  const handleCategoryClick = (title: string) => {
    setSelectedCategory(title);
    setIsSheetOpen(true);
  };



  return (
    <section ref={sectionRef} id="menu" className="py-20 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="hero-title text-3xl md:text-4xl text-firefly-black mb-4">EXPLORE OUR <span className="text-firefly-red">MENU</span></h2>
          <p className="text-gray-600 max-w-lg mx-auto">
            Discover our range of handcrafted burgers and sides, each made with premium ingredients and bold flavors.
          </p>
        </div>
        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="relative">
              <Loader2 size={64} className="text-firefly-red animate-spin" />
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <img 
                  src="/burger-top.png" 
                  alt="Firefly Burger" 
                  className="w-10 h-10 object-contain animate-pulse" 
                />
              </div>
            </div>
            <p className="mt-4 text-firefly-red font-medium">Loading menu categories...</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 place-items-center">
              {categories.map((category, index) => (
                <CategoryItem
                  key={index}
                  image={category.image}
                  title={category.title}
                  description={category.description}
                  onClick={() => handleCategoryClick(category.title)}
                  index={index}
                  isVisible={visibleCards[index] || false}
                />
              ))}
            </div>

            {/* Category Details Sheet */}
            <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
              <SheetContent side="right" className="w-full sm:w-[600px] sm:max-w-[600px] overflow-y-auto">
                {selectedCategory && categoryDetails[selectedCategory] && (
                  <>
                    <SheetHeader>
                      <SheetTitle className="text-2xl font-bold text-firefly-red">
                        {selectedCategory}
                      </SheetTitle>
                      <SheetDescription className="text-gray-600">
                        {categoryDetails[selectedCategory].description}
                      </SheetDescription>
                    </SheetHeader>

                    <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {categoryDetails[selectedCategory].items.map((item) => (
                        <div key={item.id} className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden hover:shadow-lg hover:scale-105 transition-all duration-300 cursor-pointer">
                          <div className="relative">
                            <img
                              src={item.image}
                              alt={item.name}
                              className="w-full h-48 object-cover"
                            />
                            <div className="absolute top-2 right-2 flex gap-1">
                              {item.isPopular && (
                                <span className="bg-firefly-red text-white text-xs px-2 py-1 rounded-full shadow-md">
                                  Popular
                                </span>
                              )}
                              {item.isNew && (
                                <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full shadow-md">
                                  New
                                </span>
                              )}
                            </div>
                            <div className="absolute bottom-2 right-2 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1">
                              <span className="text-lg font-bold text-firefly-red">
                                ${item.price}
                              </span>
                            </div>
                          </div>
                          <div className="p-4">
                            <h3 className="font-semibold text-lg text-gray-900 mb-2 line-clamp-1">
                              {item.name}
                            </h3>
                            <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                              {item.description}
                            </p>
                            <div className="flex items-center justify-between text-sm text-gray-500">
                              <div className="flex items-center gap-1">
                                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                <span className="font-medium">{item.rating}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                <span>{item.prepTime}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </>
                )}
              </SheetContent>
            </Sheet>
          </>
        )}
      </div>
    </section>
  );
};

export default BurgerCategories;
