import { Button } from "@/components/ui/button";
import { useState, useEffect, useRef } from "react";
import { Plus } from "lucide-react";

interface BurgerItemProps {
  name: string;
  description: string;
  price: string;
  image: string;
  spicy?: boolean;
  popular?: boolean;
  index?: number;
  isVisible?: boolean;
}

const BurgerItem = ({ name, description, price, image, spicy, popular, index, isVisible }: BurgerItemProps) => {
  return (
    <div className={`w-full aspect-[1/1.2] h-[450px] transition-all duration-500 ${isVisible ? 'animate-bounce-in opacity-100' : 'opacity-0 translate-y-10'}`} style={{ animationDelay: `${index ? index * 100 : 0}ms` }}>
      <div className="bg-white rounded-xl w-full h-full shadow-md hover:shadow-lg transition-shadow duration-300 relative overflow-hidden border border-gray-100">
        <div className="flex flex-col h-full">
          <div className="relative h-[220px] overflow-hidden">
            <img
              src={image}
              alt={name}
              className="object-cover w-full h-full"
            />

            {spicy && (
              <div className="absolute top-3 right-3">
                <span className="bg-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                  SPICY
                </span>
              </div>
            )}

            {popular && (
              <div className="absolute top-3 left-3">
                <span className="bg-yellow-500 text-black text-xs font-bold px-3 py-1 rounded-full shadow-lg">
                  POPULAR
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-col justify-center text-center p-6 flex-1">
            <div className="w-full mb-2">
              <h3 className="text-2xl font-bold mb-3 text-gray-900">{name}</h3>
            </div>
            <div className="w-full mb-2">
              <span className="text-firefly-red font-bold text-xl">{price}</span>
            </div>
            <div className="w-full">
              <p className="text-gray-600 text-sm">{description}</p>
            </div>
          </div>
        </div>

        {/* Add to Cart Plus Button - Takes half of bottom right corner */}
        <div className="absolute bottom-0 right-0">
          <Button
            className="w-16 h-16 rounded-tl-full bg-firefly-red hover:bg-red-700 text-white p-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            <Plus size={40} className="translate-x-1 translate-y-1" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const FeaturedBurgers = () => {
  const [visibleCards, setVisibleCards] = useState<boolean[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const sectionRef = useRef<HTMLElement>(null);

  const burgers = [
    {
      name: "FireFly Classic",
      description: "Signature beef patty, cheddar cheese, lettuce, tomato, special sauce on a brioche bun",
      price: "$12.99",
      image: "https://images.unsplash.com/photo-1586190848861-99aa4a171e90?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1280&q=80",
      popular: true
    },
    {
      name: "Spicy Inferno",
      description: "Double beef patty, jalapeños, pepper jack cheese, hot sauce, lettuce on a charcoal bun",
      price: "$14.99",
      image: "https://images.unsplash.com/photo-1551782450-17144efb9c50?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1169&q=80",
      spicy: true
    },
    {
      name: "Blackout Chicken",
      description: "Crispy chicken, black garlic mayo, coleslaw, pickles on a toasted potato bun",
      price: "$13.99",
      image: "https://images.unsplash.com/photo-1606755962773-d324e0a13086?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80"
    },
    {
      name: "Veggie Blaze",
      description: "Plant-based patty, avocado, roasted peppers, vegan garlic aioli on a whole grain bun",
      price: "$13.49",
      image: "https://images.unsplash.com/photo-1550317138-10000687a72b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1220&q=80"
    },
    {
      name: "Loaded Fries Deluxe",
      description: "Crispy fries topped with cheese sauce, bacon bits, jalapeños, and special sauce",
      price: "$8.99",
      image: "https://images.unsplash.com/photo-1573080496219-bb080dd4f877?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80"
    },
    {
      name: "Double Trouble",
      description: "Double beef patties, double cheese, caramelized onions, bacon, BBQ sauce",
      price: "$16.99",
      image: "https://images.unsplash.com/photo-1596662951482-0c4ba74a6df6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=687&q=80",
      popular: true
    }
  ];

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const cards = entry.target.querySelectorAll('[data-card-index]');
            const newVisibleCards = [...visibleCards];

            cards.forEach((card, index) => {
              setTimeout(() => {
                setVisibleCards(prev => {
                  const updated = [...prev];
                  updated[index] = true;
                  return updated;
                });
              }, index * 100);
            });
          }
        });
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [isLoading]);

  return (
    <section ref={sectionRef} className="py-20 bg-white relative overflow-hidden">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="hero-title text-3xl md:text-4xl text-firefly-black mb-4">FEATURED <span className="text-firefly-red">BURGERS</span></h2>
          <p className="text-gray-600 max-w-lg mx-auto">
            Our chef's selection of mouthwatering creations that will ignite your taste buds.
          </p>
        </div>

        {isLoading ? (
          <div className="flex flex-col items-center justify-center py-16">
            <div className="relative">
              <div className="w-16 h-16 border-4 border-firefly-red border-t-transparent rounded-full animate-spin"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 bg-white rounded-full"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center">
                <img
                  src="/burger-top.png"
                  alt="Firefly Burger"
                  className="w-10 h-10 object-contain animate-pulse"
                />
              </div>
            </div>
            <p className="mt-4 text-firefly-red font-medium">Loading featured burgers...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 place-items-center">
            {burgers.map((burger, index) => (
              <div key={index} data-card-index={index}>
                <BurgerItem
                  name={burger.name}
                  description={burger.description}
                  price={burger.price}
                  image={burger.image}
                  spicy={burger.spicy}
                  popular={burger.popular}
                  index={index}
                  isVisible={visibleCards[index] || false}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedBurgers;
